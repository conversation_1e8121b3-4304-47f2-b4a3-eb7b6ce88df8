import React, { useState, useEffect } from 'react';
import { useWebSocketDashboard } from '../context/WebSocketContext';
import { <PERSON><PERSON>, <PERSON>, CardBody, CardHeader, Chip, Divider } from '@heroui/react';
import { FaWifi, FaWifiSlash, Fa<PERSON><PERSON><PERSON>, FaBolt, FaSync, FaTrash } from 'react-icons/fa';

const RealTimeTestPanel: React.FC = () => {
  const {
    isConnected,
    connectionState,
    updateCount,
    lastUpdate,
    dashboardUpdates,
    reconnect,
    clearUpdates,
  } = useWebSocketDashboard();

  const [testEvents, setTestEvents] = useState<string[]>([]);

  // Add test event to log
  const addTestEvent = (event: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestEvents(prev => [`[${timestamp}] ${event}`, ...prev.slice(0, 19)]);
  };

  // Monitor connection changes
  useEffect(() => {
    addTestEvent(`Connection state changed: ${connectionState}`);
  }, [connectionState]);

  // Monitor updates
  useEffect(() => {
    if (lastUpdate) {
      addTestEvent(`Dashboard update received: ${lastUpdate.type} at ${new Date(lastUpdate.timestamp).toLocaleTimeString()}`);
    }
  }, [lastUpdate]);

  const getConnectionIcon = () => {
    switch (connectionState) {
      case 'connected':
        return <FaWifi className="text-green-500" />;
      case 'connecting':
      case 'reconnecting':
        return <FaSpinner className="text-yellow-500 animate-spin" />;
      default:
        return <FaWifiSlash className="text-red-500" />;
    }
  };

  const getConnectionColor = () => {
    switch (connectionState) {
      case 'connected':
        return 'success';
      case 'connecting':
      case 'reconnecting':
        return 'warning';
      default:
        return 'danger';
    }
  };

  const simulateUpdate = (type: 'stats' | 'transactions' | 'bookings') => {
    addTestEvent(`Simulating ${type} update...`);
    // In a real scenario, this would trigger a backend event
    // For testing, we can manually trigger query invalidation
    console.log(`Simulating ${type} update`);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="flex gap-3">
        <div className="flex flex-col">
          <p className="text-md font-semibold">Real-Time Dashboard Test Panel</p>
          <p className="text-small text-default-500">Monitor WebSocket connection and updates</p>
        </div>
      </CardHeader>
      <Divider />
      <CardBody>
        <div className="space-y-4">
          {/* Connection Status */}
          <div className="flex items-center justify-between">
            <span className="font-medium">Connection Status:</span>
            <Chip
              color={getConnectionColor() as any}
              variant="flat"
              startContent={getConnectionIcon()}
            >
              {connectionState}
            </Chip>
          </div>

          {/* Update Counter */}
          <div className="flex items-center justify-between">
            <span className="font-medium">Updates Received:</span>
            <Chip color="primary" variant="flat">
              {updateCount}
            </Chip>
          </div>

          {/* Last Update */}
          {lastUpdate && (
            <div className="flex items-center justify-between">
              <span className="font-medium">Last Update:</span>
              <Chip
                color="success"
                variant="flat"
                startContent={<FaBolt />}
              >
                {lastUpdate.type} at {new Date(lastUpdate.timestamp).toLocaleTimeString()}
              </Chip>
            </div>
          )}

          <Divider />

          {/* Control Buttons */}
          <div className="flex gap-2 flex-wrap">
            <Button
              color="primary"
              variant="flat"
              size="sm"
              startContent={<FaSync />}
              onPress={reconnect}
              isDisabled={connectionState === 'connecting' || connectionState === 'reconnecting'}
            >
              Reconnect
            </Button>
            <Button
              color="secondary"
              variant="flat"
              size="sm"
              onPress={() => simulateUpdate('stats')}
            >
              Simulate Stats Update
            </Button>
            <Button
              color="secondary"
              variant="flat"
              size="sm"
              onPress={() => simulateUpdate('transactions')}
            >
              Simulate Transactions Update
            </Button>
            <Button
              color="secondary"
              variant="flat"
              size="sm"
              onPress={() => simulateUpdate('bookings')}
            >
              Simulate Bookings Update
            </Button>
            <Button
              color="danger"
              variant="flat"
              size="sm"
              startContent={<FaTrash />}
              onPress={() => {
                clearUpdates();
                setTestEvents([]);
                addTestEvent('Cleared all updates and events');
              }}
            >
              Clear
            </Button>
          </div>

          <Divider />

          {/* Recent Updates */}
          <div>
            <h4 className="font-medium mb-2">Recent Dashboard Updates ({dashboardUpdates.length})</h4>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {dashboardUpdates.length === 0 ? (
                <p className="text-small text-default-500">No updates received yet</p>
              ) : (
                dashboardUpdates.slice(0, 5).map((update, index) => (
                  <div key={index} className="text-small bg-default-100 p-2 rounded">
                    <span className="font-medium text-primary">{update.type}</span>
                    <span className="text-default-500 ml-2">
                      {new Date(update.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                ))
              )}
            </div>
          </div>

          <Divider />

          {/* Test Event Log */}
          <div>
            <h4 className="font-medium mb-2">Test Event Log ({testEvents.length})</h4>
            <div className="space-y-1 max-h-40 overflow-y-auto">
              {testEvents.length === 0 ? (
                <p className="text-small text-default-500">No events logged yet</p>
              ) : (
                testEvents.map((event, index) => (
                  <div key={index} className="text-small font-mono bg-default-50 p-1 rounded">
                    {event}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default RealTimeTestPanel;
