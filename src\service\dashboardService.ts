/* eslint-disable @typescript-eslint/no-explicit-any */
import { apiClient } from '../api';
import logger from '../utils/logger';

// Dashboard interfaces
export interface DashboardStats {
  totalOrders: {
    value: number;
    percentage: string;
    trend: 'up' | 'down' | 'neutral';
  };
  totalSpend: {
    value: number;
    percentage: string;
    trend: 'up' | 'down' | 'neutral';
  };
  wallet: {
    value: number;
    percentage: string;
    trend: 'up' | 'down' | 'neutral';
  };
  totalSavings: {
    value: number;
    percentage: string;
    trend: 'up' | 'down' | 'neutral';
  };
}

export interface Transaction {
  id: string;
  type: 'Service Booking' | 'Service Refund' | 'Wallet Topup' | 'Payment';
  date: string;
  amount: number;
  status: 'Completed' | 'Pending' | 'In Process' | 'Cancelled';
  description?: string;
  bookingId?: string;
  serviceId?: string;
}

export interface DashboardTransaction {
  transactions: Transaction[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface RecentBooking {
  id: string;
  service: string;
  serviceName?: string;
  date: string;
  customerName: string;
  customerEmail: string;
  status: 'Pending' | 'Confirmed' | 'Completed' | 'Cancelled' | 'In Process';
  amount?: number;
  providerId?: string;
  serviceId?: string;
}

export interface DashboardBookings {
  bookings: RecentBooking[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Get dashboard statistics for the current user
 */
export const getDashboardStats = async (): Promise<DashboardStats> => {
  try {
    console.log('Fetching dashboard statistics...');
    const response = await apiClient.get('/api/v1/dashboard/stats');
    console.log('Dashboard stats fetched successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching dashboard stats:', error);
    logger.error('Error fetching dashboard stats:', error);
    
    // Return fallback data if API fails
    console.log('Using fallback dashboard stats');
    return getFallbackStats();
  }
};

/**
 * Get recent transactions for dashboard
 */
export const getDashboardTransactions = async (params: {
  page?: number;
  limit?: number;
} = {}): Promise<DashboardTransaction> => {
  try {
    const { page = 1, limit = 5 } = params;
    console.log(`Fetching dashboard transactions - page: ${page}, limit: ${limit}`);

    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    const response = await apiClient.get(`/api/v1/dashboard/transactions?${queryParams.toString()}`);
    console.log('Dashboard transactions fetched successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching dashboard transactions:', error);
    logger.error('Error fetching dashboard transactions:', error);
    
    // Return fallback data if API fails
    console.log('Using fallback dashboard transactions');
    return getFallbackTransactions();
  }
};

/**
 * Get recent bookings for dashboard
 */
export const getDashboardBookings = async (params: {
  page?: number;
  limit?: number;
} = {}): Promise<DashboardBookings> => {
  try {
    const { page = 1, limit = 6 } = params;
    console.log(`Fetching dashboard bookings - page: ${page}, limit: ${limit}`);

    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    const response = await apiClient.get(`/api/v1/dashboard/bookings?${queryParams.toString()}`);
    console.log('Dashboard bookings fetched successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching dashboard bookings:', error);
    logger.error('Error fetching dashboard bookings:', error);
    
    // Return fallback data if API fails
    console.log('Using fallback dashboard bookings');
    return getFallbackBookings();
  }
};

/**
 * Fallback stats when API is unavailable
 */
const getFallbackStats = (): DashboardStats => {
  return {
    totalOrders: {
      value: 27,
      percentage: '16%',
      trend: 'up'
    },
    totalSpend: {
      value: 2500,
      percentage: '5%',
      trend: 'down'
    },
    wallet: {
      value: 200,
      percentage: '5%',
      trend: 'down'
    },
    totalSavings: {
      value: 354,
      percentage: '16%',
      trend: 'up'
    }
  };
};

/**
 * Fallback transactions when API is unavailable
 */
const getFallbackTransactions = (): DashboardTransaction => {
  return {
    transactions: [
      {
        id: '1',
        type: 'Service Booking',
        date: '02 Sep 2022, 09:12 AM',
        amount: 280.00,
        status: 'In Process',
      },
      {
        id: '2',
        type: 'Service Refund',
        date: '02 Sep 2022, 04:36 PM',
        amount: 395.00,
        status: 'Completed',
      },
      {
        id: '3',
        type: 'Wallet Topup',
        date: '01 Sep 2022, 10:00 AM',
        amount: 1000.00,
        status: 'Pending',
      },
      {
        id: '4',
        type: 'Service Booking',
        date: '31 Aug 2022, 11:17 AM',
        amount: 598.65,
        status: 'Cancelled',
      },
    ],
    total: 4,
    page: 1,
    limit: 5,
    totalPages: 1
  };
};

/**
 * Fallback bookings when API is unavailable
 */
const getFallbackBookings = (): DashboardBookings => {
  return {
    bookings: [
      {
        id: '1',
        service: 'Computer Repair',
        date: '10 Nov 2022',
        customerName: 'John Smith',
        customerEmail: '<EMAIL>',
        status: 'In Process',
      },
      {
        id: '2',
        service: 'Car Repair',
        date: '15 Oct 2022',
        customerName: 'Timothy',
        customerEmail: '<EMAIL>',
        status: 'Pending',
      },
      {
        id: '3',
        service: 'Interior Designing',
        date: '18 Oct 2022',
        customerName: 'Jordan',
        customerEmail: '<EMAIL>',
        status: 'Completed',
      },
      {
        id: '4',
        service: 'Steam Car Wash',
        date: '28 Oct 2022',
        customerName: 'Armand',
        customerEmail: '<EMAIL>',
        status: 'Cancelled',
      },
    ],
    total: 4,
    page: 1,
    limit: 6,
    totalPages: 1
  };
};
