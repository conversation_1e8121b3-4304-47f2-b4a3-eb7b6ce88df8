import { io, Socket } from 'socket.io-client';
import { getToken } from '../tokenprovider';

export interface DashboardUpdateEvent {
  type: 'stats' | 'transactions' | 'bookings';
  data: any;
  timestamp: string;
}

export interface WebSocketEvents {
  // Dashboard events
  'dashboard:stats:updated': (data: any) => void;
  'dashboard:transactions:updated': (data: any) => void;
  'dashboard:bookings:updated': (data: any) => void;
  'dashboard:update': (event: DashboardUpdateEvent) => void;
  
  // Connection events
  'connect': () => void;
  'disconnect': () => void;
  'connect_error': (error: Error) => void;
  'reconnect': (attemptNumber: number) => void;
  'reconnect_error': (error: Error) => void;
  'reconnect_failed': () => void;
}

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private eventListeners: Map<string, Set<Function>> = new Map();

  constructor() {
    this.connect();
  }

  /**
   * Connect to WebSocket server
   */
  connect(): void {
    if (this.socket?.connected || this.isConnecting) {
      console.log('WebSocket already connected or connecting');
      return;
    }

    this.isConnecting = true;
    const token = getToken();
    const backendUrl = import.meta.env.VITE_APP_BACKEND_PORT;

    if (!backendUrl) {
      console.error('Backend URL not configured');
      this.isConnecting = false;
      return;
    }

    console.log('Connecting to WebSocket server...');

    this.socket = io(backendUrl, {
      auth: {
        token: token || '',
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: this.reconnectDelay,
      reconnectionDelayMax: 5000,
      maxHttpBufferSize: 1e6,
    });

    this.setupEventListeners();
    this.isConnecting = false;
  }

  /**
   * Setup WebSocket event listeners
   */
  private setupEventListeners(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('✅ WebSocket connected successfully');
      this.reconnectAttempts = 0;
      this.emit('connect');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ WebSocket disconnected:', reason);
      this.emit('disconnect');
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ WebSocket connection error:', error);
      this.emit('connect_error', error);
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.log(`🔄 WebSocket reconnected after ${attemptNumber} attempts`);
      this.reconnectAttempts = 0;
      this.emit('reconnect', attemptNumber);
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('❌ WebSocket reconnection error:', error);
      this.reconnectAttempts++;
      this.emit('reconnect_error', error);
    });

    this.socket.on('reconnect_failed', () => {
      console.error('❌ WebSocket reconnection failed after maximum attempts');
      this.emit('reconnect_failed');
    });

    // Dashboard-specific events
    this.socket.on('dashboard:stats:updated', (data) => {
      console.log('📊 Dashboard stats updated:', data);
      this.emit('dashboard:stats:updated', data);
      this.emit('dashboard:update', {
        type: 'stats',
        data,
        timestamp: new Date().toISOString(),
      });
    });

    this.socket.on('dashboard:transactions:updated', (data) => {
      console.log('💰 Dashboard transactions updated:', data);
      this.emit('dashboard:transactions:updated', data);
      this.emit('dashboard:update', {
        type: 'transactions',
        data,
        timestamp: new Date().toISOString(),
      });
    });

    this.socket.on('dashboard:bookings:updated', (data) => {
      console.log('📅 Dashboard bookings updated:', data);
      this.emit('dashboard:bookings:updated', data);
      this.emit('dashboard:update', {
        type: 'bookings',
        data,
        timestamp: new Date().toISOString(),
      });
    });
  }

  /**
   * Subscribe to dashboard updates for a specific user
   */
  subscribeToDashboard(userId: string): void {
    if (!this.socket?.connected) {
      console.warn('WebSocket not connected, cannot subscribe to dashboard');
      return;
    }

    console.log(`🔔 Subscribing to dashboard updates for user: ${userId}`);
    this.socket.emit('dashboard:subscribe', { userId });
  }

  /**
   * Unsubscribe from dashboard updates
   */
  unsubscribeFromDashboard(userId: string): void {
    if (!this.socket?.connected) {
      console.warn('WebSocket not connected, cannot unsubscribe from dashboard');
      return;
    }

    console.log(`🔕 Unsubscribing from dashboard updates for user: ${userId}`);
    this.socket.emit('dashboard:unsubscribe', { userId });
  }

  /**
   * Add event listener
   */
  on<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(listener);
  }

  /**
   * Remove event listener
   */
  off<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  /**
   * Emit event to listeners
   */
  private emit(event: string, ...args: any[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach((listener) => {
        try {
          listener(...args);
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get connection status
   */
  get isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Get connection state
   */
  get connectionState(): 'connected' | 'disconnected' | 'connecting' | 'reconnecting' {
    if (!this.socket) return 'disconnected';
    if (this.socket.connected) return 'connected';
    if (this.isConnecting) return 'connecting';
    if (this.reconnectAttempts > 0) return 'reconnecting';
    return 'disconnected';
  }

  /**
   * Manually reconnect
   */
  reconnect(): void {
    if (this.socket) {
      console.log('🔄 Manually reconnecting WebSocket...');
      this.socket.disconnect();
      this.socket.connect();
    } else {
      this.connect();
    }
  }

  /**
   * Disconnect WebSocket
   */
  disconnect(): void {
    if (this.socket) {
      console.log('🔌 Disconnecting WebSocket...');
      this.socket.disconnect();
      this.socket = null;
    }
    this.eventListeners.clear();
  }

  /**
   * Send a custom event to the server
   */
  emit(event: string, data?: any): void {
    if (!this.socket?.connected) {
      console.warn(`Cannot emit ${event}: WebSocket not connected`);
      return;
    }

    this.socket.emit(event, data);
  }
}

// Create singleton instance
const websocketService = new WebSocketService();

export default websocketService;
