import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useAuth } from 'react-oidc-context';
import websocketService, { DashboardUpdateEvent } from '../service/websocketService';

export interface WebSocketContextType {
  // Connection state
  isConnected: boolean;
  connectionState: 'connected' | 'disconnected' | 'connecting' | 'reconnecting';
  
  // Real-time data
  lastUpdate: DashboardUpdateEvent | null;
  updateCount: number;
  
  // Actions
  reconnect: () => void;
  subscribeToDashboard: () => void;
  unsubscribeFromDashboard: () => void;
  
  // Event handlers
  onDashboardUpdate: (callback: (event: DashboardUpdateEvent) => void) => () => void;
  onConnectionChange: (callback: (connected: boolean) => void) => () => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

export interface WebSocketProviderProps {
  children: ReactNode;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const auth = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState<'connected' | 'disconnected' | 'connecting' | 'reconnecting'>('disconnected');
  const [lastUpdate, setLastUpdate] = useState<DashboardUpdateEvent | null>(null);
  const [updateCount, setUpdateCount] = useState(0);

  // Get user ID from auth
  const userId = auth?.user?.profile?.sub || auth?.user?.profile?.preferred_username;

  useEffect(() => {
    // Set initial connection state
    setIsConnected(websocketService.isConnected);
    setConnectionState(websocketService.connectionState);

    // Connection event handlers
    const handleConnect = () => {
      console.log('🟢 WebSocket Context: Connected');
      setIsConnected(true);
      setConnectionState('connected');
      
      // Auto-subscribe to dashboard if user is authenticated
      if (userId) {
        websocketService.subscribeToDashboard(userId);
      }
    };

    const handleDisconnect = () => {
      console.log('🔴 WebSocket Context: Disconnected');
      setIsConnected(false);
      setConnectionState('disconnected');
    };

    const handleConnectError = (error: Error) => {
      console.error('🔴 WebSocket Context: Connection error', error);
      setIsConnected(false);
      setConnectionState('disconnected');
    };

    const handleReconnect = (attemptNumber: number) => {
      console.log(`🟡 WebSocket Context: Reconnected after ${attemptNumber} attempts`);
      setIsConnected(true);
      setConnectionState('connected');
      
      // Re-subscribe to dashboard after reconnection
      if (userId) {
        websocketService.subscribeToDashboard(userId);
      }
    };

    const handleReconnectError = () => {
      console.log('🟡 WebSocket Context: Reconnecting...');
      setConnectionState('reconnecting');
    };

    const handleReconnectFailed = () => {
      console.error('🔴 WebSocket Context: Reconnection failed');
      setIsConnected(false);
      setConnectionState('disconnected');
    };

    // Dashboard update handler
    const handleDashboardUpdate = (event: DashboardUpdateEvent) => {
      console.log('📊 WebSocket Context: Dashboard update received', event);
      setLastUpdate(event);
      setUpdateCount(prev => prev + 1);
    };

    // Register event listeners
    websocketService.on('connect', handleConnect);
    websocketService.on('disconnect', handleDisconnect);
    websocketService.on('connect_error', handleConnectError);
    websocketService.on('reconnect', handleReconnect);
    websocketService.on('reconnect_error', handleReconnectError);
    websocketService.on('reconnect_failed', handleReconnectFailed);
    websocketService.on('dashboard:update', handleDashboardUpdate);

    // Cleanup on unmount
    return () => {
      websocketService.off('connect', handleConnect);
      websocketService.off('disconnect', handleDisconnect);
      websocketService.off('connect_error', handleConnectError);
      websocketService.off('reconnect', handleReconnect);
      websocketService.off('reconnect_error', handleReconnectError);
      websocketService.off('reconnect_failed', handleReconnectFailed);
      websocketService.off('dashboard:update', handleDashboardUpdate);
      
      // Unsubscribe from dashboard
      if (userId) {
        websocketService.unsubscribeFromDashboard(userId);
      }
    };
  }, [userId]);

  // Subscribe to dashboard updates when user changes
  useEffect(() => {
    if (isConnected && userId) {
      websocketService.subscribeToDashboard(userId);
    }
    
    return () => {
      if (userId) {
        websocketService.unsubscribeFromDashboard(userId);
      }
    };
  }, [isConnected, userId]);

  // Context methods
  const reconnect = () => {
    console.log('🔄 WebSocket Context: Manual reconnect requested');
    setConnectionState('connecting');
    websocketService.reconnect();
  };

  const subscribeToDashboard = () => {
    if (userId) {
      websocketService.subscribeToDashboard(userId);
    }
  };

  const unsubscribeFromDashboard = () => {
    if (userId) {
      websocketService.unsubscribeFromDashboard(userId);
    }
  };

  const onDashboardUpdate = (callback: (event: DashboardUpdateEvent) => void) => {
    websocketService.on('dashboard:update', callback);
    
    // Return cleanup function
    return () => {
      websocketService.off('dashboard:update', callback);
    };
  };

  const onConnectionChange = (callback: (connected: boolean) => void) => {
    const handleConnect = () => callback(true);
    const handleDisconnect = () => callback(false);
    
    websocketService.on('connect', handleConnect);
    websocketService.on('disconnect', handleDisconnect);
    
    // Return cleanup function
    return () => {
      websocketService.off('connect', handleConnect);
      websocketService.off('disconnect', handleDisconnect);
    };
  };

  const contextValue: WebSocketContextType = {
    isConnected,
    connectionState,
    lastUpdate,
    updateCount,
    reconnect,
    subscribeToDashboard,
    unsubscribeFromDashboard,
    onDashboardUpdate,
    onConnectionChange,
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
};

/**
 * Hook to use WebSocket context
 */
export const useWebSocket = (): WebSocketContextType => {
  const context = useContext(WebSocketContext);
  if (context === undefined) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

/**
 * Hook for dashboard-specific WebSocket functionality
 */
export const useWebSocketDashboard = () => {
  const webSocket = useWebSocket();
  
  const [dashboardUpdates, setDashboardUpdates] = useState<DashboardUpdateEvent[]>([]);
  const [lastStatsUpdate, setLastStatsUpdate] = useState<any>(null);
  const [lastTransactionsUpdate, setLastTransactionsUpdate] = useState<any>(null);
  const [lastBookingsUpdate, setLastBookingsUpdate] = useState<any>(null);

  useEffect(() => {
    const cleanup = webSocket.onDashboardUpdate((event) => {
      // Add to updates history (keep last 10)
      setDashboardUpdates(prev => [event, ...prev.slice(0, 9)]);
      
      // Update specific data based on type
      switch (event.type) {
        case 'stats':
          setLastStatsUpdate(event.data);
          break;
        case 'transactions':
          setLastTransactionsUpdate(event.data);
          break;
        case 'bookings':
          setLastBookingsUpdate(event.data);
          break;
      }
    });

    return cleanup;
  }, [webSocket]);

  return {
    ...webSocket,
    dashboardUpdates,
    lastStatsUpdate,
    lastTransactionsUpdate,
    lastBookingsUpdate,
    clearUpdates: () => setDashboardUpdates([]),
  };
};
