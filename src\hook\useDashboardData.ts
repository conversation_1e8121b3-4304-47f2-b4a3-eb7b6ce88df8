/* eslint-disable @typescript-eslint/no-explicit-any */
import { useQuery } from '@tanstack/react-query';
import {
  getDashboardStats,
  getDashboardTransactions,
  getDashboardBookings,
  DashboardStats,
  DashboardTransaction,
  DashboardBookings,
} from '../service/dashboardService';

/**
 * Hook for fetching dashboard statistics with real-time updates
 */
export const useDashboardStats = () => {
  return useQuery<DashboardStats>({
    queryKey: ['dashboardStats'],
    queryFn: getDashboardStats,
    staleTime: 1000 * 60 * 2, // 2 minutes
    refetchInterval: 1000 * 60 * 5, // Refetch every 5 minutes
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook for fetching dashboard transactions with real-time updates
 */
export const useDashboardTransactions = (params: {
  page?: number;
  limit?: number;
} = {}) => {
  return useQuery<DashboardTransaction>({
    queryKey: ['dashboardTransactions', params],
    queryFn: () => getDashboardTransactions(params),
    staleTime: 1000 * 60 * 1, // 1 minute
    refetchInterval: 1000 * 60 * 3, // Refetch every 3 minutes
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook for fetching dashboard bookings with real-time updates
 */
export const useDashboardBookings = (params: {
  page?: number;
  limit?: number;
} = {}) => {
  return useQuery<DashboardBookings>({
    queryKey: ['dashboardBookings', params],
    queryFn: () => getDashboardBookings(params),
    staleTime: 1000 * 60 * 1, // 1 minute
    refetchInterval: 1000 * 60 * 2, // Refetch every 2 minutes
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook for manual refresh of all dashboard data
 */
export const useDashboardRefresh = () => {
  const statsQuery = useDashboardStats();
  const transactionsQuery = useDashboardTransactions();
  const bookingsQuery = useDashboardBookings();

  const refreshAll = async () => {
    await Promise.all([
      statsQuery.refetch(),
      transactionsQuery.refetch(),
      bookingsQuery.refetch(),
    ]);
  };

  const isRefreshing = statsQuery.isFetching || transactionsQuery.isFetching || bookingsQuery.isFetching;

  return {
    refreshAll,
    isRefreshing,
    stats: statsQuery,
    transactions: transactionsQuery,
    bookings: bookingsQuery,
  };
};

/**
 * Hook for real-time dashboard data with automatic refresh and error handling
 */
export const useRealTimeDashboard = () => {
  const statsQuery = useDashboardStats();
  const transactionsQuery = useDashboardTransactions({ limit: 4 });
  const bookingsQuery = useDashboardBookings({ limit: 4 });

  // Calculate overall loading state
  const isLoading = statsQuery.isLoading || transactionsQuery.isLoading || bookingsQuery.isLoading;
  
  // Calculate overall error state
  const hasError = statsQuery.isError || transactionsQuery.isError || bookingsQuery.isError;
  
  // Get the first error encountered
  const error = statsQuery.error || transactionsQuery.error || bookingsQuery.error;

  // Calculate if any data is being fetched (for refresh indicators)
  const isFetching = statsQuery.isFetching || transactionsQuery.isFetching || bookingsQuery.isFetching;

  // Manual refresh function
  const refresh = async () => {
    await Promise.all([
      statsQuery.refetch(),
      transactionsQuery.refetch(),
      bookingsQuery.refetch(),
    ]);
  };

  // Format stats data for display
  const formatStatsForDisplay = (stats: DashboardStats | undefined) => {
    if (!stats) return [];

    return [
      {
        title: 'Total Orders',
        value: stats.totalOrders.value.toString(),
        percentage: stats.totalOrders.percentage,
        color: stats.totalOrders.trend === 'up' ? 'text-green-500' : stats.totalOrders.trend === 'down' ? 'text-red-500' : 'text-gray-500',
        bg: 'bg-pink-100',
        icon: 'FaShoppingCart',
      },
      {
        title: 'Total Spend',
        value: `$${stats.totalSpend.value.toLocaleString()}`,
        percentage: stats.totalSpend.percentage,
        color: stats.totalSpend.trend === 'up' ? 'text-green-500' : stats.totalSpend.trend === 'down' ? 'text-red-500' : 'text-gray-500',
        bg: 'bg-red-100',
        icon: 'FaMoneyBillWave',
      },
      {
        title: 'Wallet',
        value: `$${stats.wallet.value.toLocaleString()}`,
        percentage: stats.wallet.percentage,
        color: stats.wallet.trend === 'up' ? 'text-green-500' : stats.wallet.trend === 'down' ? 'text-red-500' : 'text-gray-500',
        bg: 'bg-red-100',
        icon: 'FaWallet',
      },
      {
        title: 'Total Savings',
        value: `$${stats.totalSavings.value.toLocaleString()}`,
        percentage: stats.totalSavings.percentage,
        color: stats.totalSavings.trend === 'up' ? 'text-green-500' : stats.totalSavings.trend === 'down' ? 'text-red-500' : 'text-gray-500',
        bg: 'bg-green-100',
        icon: 'FaPiggyBank',
      },
    ];
  };

  // Format transactions for display
  const formatTransactionsForDisplay = (transactions: DashboardTransaction | undefined) => {
    if (!transactions?.transactions) return [];

    return transactions.transactions.map(transaction => ({
      type: transaction.type,
      date: transaction.date,
      amount: `$${transaction.amount.toFixed(2)}`,
      status: transaction.status,
    }));
  };

  // Format bookings for display
  const formatBookingsForDisplay = (bookings: DashboardBookings | undefined) => {
    if (!bookings?.bookings) return [];

    return bookings.bookings.map(booking => ({
      service: booking.service || booking.serviceName || 'Unknown Service',
      date: booking.date,
      name: booking.customerName,
      email: booking.customerEmail,
      status: booking.status,
    }));
  };

  return {
    // Data
    stats: formatStatsForDisplay(statsQuery.data),
    transactions: formatTransactionsForDisplay(transactionsQuery.data),
    bookings: formatBookingsForDisplay(bookingsQuery.data),
    
    // Raw data (if needed)
    rawStats: statsQuery.data,
    rawTransactions: transactionsQuery.data,
    rawBookings: bookingsQuery.data,
    
    // Loading states
    isLoading,
    isFetching,
    
    // Error states
    hasError,
    error,
    
    // Actions
    refresh,
    
    // Individual query states (for granular control)
    queries: {
      stats: statsQuery,
      transactions: transactionsQuery,
      bookings: bookingsQuery,
    },
  };
};
