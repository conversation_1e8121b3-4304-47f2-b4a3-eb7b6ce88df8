import {
  FaShoppingCart,
  FaWallet,
  FaPiggyBank,
  FaMoneyBillWave,
  FaEye,
  Fa<PERSON>ser,
  FaSync,
  FaSpinner,
  FaExclamationTriangle,
  FaWifi,
  FaWifiSlash,
  FaBolt,
  FaCircle,
} from 'react-icons/fa';
import BreadCrumb from '../../../components/common/breadcrumb/breadCrumb';
import CustomButton from '../../../components/CustomButton';
import { Chip, Spinner } from '@heroui/react';
import { useRealTimeDashboard } from '../../../../hook/useDashboardData';
import { toast } from 'react-hot-toast';
import RealTimeTestPanel from '../../../../components/RealTimeTestPanel';

export default function Dashboard() {
  // Use real-time dashboard data
  const {
    stats,
    transactions,
    bookings,
    isLoading,
    isFetching,
    hasError,
    error,
    refresh,
    realTime,
  } = useRealTimeDashboard();

  // Handle refresh
  const handleRefresh = async () => {
    try {
      await refresh();
      toast.success('Dashboard data refreshed successfully!');
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
      toast.error('Failed to refresh dashboard data');
    }
  };

  // Get icon component for stats
  const getStatIcon = (iconName: string) => {
    switch (iconName) {
      case 'FaShoppingCart':
        return <FaShoppingCart />;
      case 'FaMoneyBillWave':
        return <FaMoneyBillWave />;
      case 'FaWallet':
        return <FaWallet />;
      case 'FaPiggyBank':
        return <FaPiggyBank />;
      default:
        return <FaShoppingCart />;
    }
  };

  // Loading and error states
  if (isLoading) {
    return (
      <div className="max-w-screen-xl mx-auto p-3 sm:p-4 md:p-6 w-full">
        <BreadCrumb title="Dashboard" item1="Customer" />
        <h2 className="text-2xl font-semibold text-gray-800 mt-4 mb-6">Dashboard</h2>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Spinner size="lg" color="primary" />
            <p className="mt-4 text-gray-600">Loading dashboard data...</p>
          </div>
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="max-w-screen-xl mx-auto p-3 sm:p-4 md:p-6 w-full">
        <BreadCrumb title="Dashboard" item1="Customer" />
        <h2 className="text-2xl font-semibold text-gray-800 mt-4 mb-6">Dashboard</h2>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <FaExclamationTriangle className="text-red-500 text-4xl mx-auto mb-4" />
            <p className="text-gray-600 mb-4">Failed to load dashboard data</p>
            <CustomButton
              label="Retry"
              color="primary"
              variant="solid"
              onPress={handleRefresh}
              startContent={<FaSync />}
            />
          </div>
        </div>
      </div>
    );
  }

  // Get color classes for status
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "success";
      case "Pending":
        return "warning";
      case "In Process":
        return "primary";
      case "Cancelled":
        return "danger";
      default:
        return "default";
    }
  };

  // StatusChip component for consistent status display
  const StatusChip = ({ status }: { status: string }) => (
    <Chip
      color={getStatusColor(status) as any}
      variant="flat"
      size="sm"
      radius="sm"
      className="font-medium"
    >
      {status}
    </Chip>
  );

  // Real-time connection status component
  const ConnectionStatus = () => {
    const getConnectionIcon = () => {
      switch (realTime.connectionState) {
        case 'connected':
          return <FaWifi className="text-green-500" />;
        case 'connecting':
        case 'reconnecting':
          return <FaSpinner className="text-yellow-500 animate-spin" />;
        default:
          return <FaWifiSlash className="text-red-500" />;
      }
    };

    const getConnectionText = () => {
      switch (realTime.connectionState) {
        case 'connected':
          return 'Live';
        case 'connecting':
          return 'Connecting...';
        case 'reconnecting':
          return 'Reconnecting...';
        default:
          return 'Offline';
      }
    };

    const getConnectionColor = () => {
      switch (realTime.connectionState) {
        case 'connected':
          return 'success';
        case 'connecting':
        case 'reconnecting':
          return 'warning';
        default:
          return 'danger';
      }
    };

    return (
      <Chip
        color={getConnectionColor() as any}
        variant="flat"
        size="sm"
        startContent={getConnectionIcon()}
        className="font-medium"
      >
        {getConnectionText()}
        {realTime.isConnected && realTime.updateCount > 0 && (
          <span className="ml-1 text-xs">({realTime.updateCount})</span>
        )}
      </Chip>
    );
  };

  // Real-time update indicator
  const RealtimeIndicator = ({ isActive }: { isActive: boolean }) => (
    isActive ? (
      <div className="absolute top-1 right-1 flex items-center">
        <FaBolt className="text-yellow-500 text-xs animate-pulse" />
        <FaCircle className="text-green-500 text-xs ml-1 animate-ping" />
      </div>
    ) : null
  );

  return (
    <div className="max-w-screen-xl mx-auto p-3 sm:p-4 md:p-6 w-full">
      {/* Breadcrumb & Title */}
      <BreadCrumb title="Dashboard" item1="Customer" />
      <div className="flex justify-between items-center mt-4 mb-6">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-semibold text-gray-800">Dashboard</h2>
          <ConnectionStatus />
        </div>
        <div className="flex items-center gap-3">
          {realTime.hasRecentUpdate && (
            <Chip
              color="success"
              variant="flat"
              size="sm"
              startContent={<FaBolt className="text-yellow-500" />}
              className="font-medium animate-pulse"
            >
              Live Updates
            </Chip>
          )}
          <CustomButton
            label={isFetching ? "Refreshing..." : "Refresh"}
            color="primary"
            variant="light"
            size="sm"
            startContent={isFetching ? <FaSpinner className="animate-spin" /> : <FaSync />}
            onPress={handleRefresh}
            isDisabled={isFetching}
          />
        </div>
      </div>

      {/* Stats Cards Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8">
        {stats.map((stat, index) => (
          <div
            key={index}
            className="bg-white p-4 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-lg flex items-center justify-between relative"
          >
            {isFetching && (
              <div className="absolute top-2 right-2">
                <FaSpinner className="text-blue-500 animate-spin text-xs" />
              </div>
            )}
            <RealtimeIndicator isActive={realTime.indicators.stats} />
            <div className={`p-3 rounded-full ${stat.bg} text-lg flex items-center justify-center`}>
              {getStatIcon(stat.icon)}
            </div>
            <div className="flex-1 ml-4">
              <p className="text-gray-500 text-sm font-medium">{stat.title}</p>
              <p className="text-xl font-semibold">{stat.value}</p>
            </div>
            <span
              className={`px-3 py-1 text-xs font-semibold rounded-full ${stat.color} bg-opacity-20`}
            >
              {stat.percentage}
            </span>
          </div>
        ))}
      </div>

      {/* Transactions & Bookings Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Transactions */}
        <div className="bg-white p-5 md:p-6 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-lg relative">
          {isFetching && (
            <div className="absolute top-4 right-4">
              <FaSpinner className="text-blue-500 animate-spin text-sm" />
            </div>
          )}
          <RealtimeIndicator isActive={realTime.indicators.transactions} />
          <div className="flex justify-between items-center mb-5">
            <div className="flex items-center gap-3">
              <h3 className="font-bold text-lg text-gray-800">Recent Transactions</h3>
              {realTime.indicators.transactions && (
                <Chip
                  color="success"
                  variant="dot"
                  size="sm"
                  className="animate-pulse"
                >
                  Updated
                </Chip>
              )}
            </div>
            <CustomButton
              label="View All"
              color="primary"
              variant="light"
              size="sm"
              endContent={<FaEye />}
              onPress={() => console.log('View all transactions')}
            />
          </div>
          <div className="space-y-3">
            {transactions.map((transaction, index) => (
              <div
                key={index}
                className="flex justify-between items-center bg-gray-50 hover:bg-gray-100 transition-colors duration-200 p-4 rounded-lg border border-gray-100"
              >
                <div className="flex-1">
                  <h4 className="text-base md:text-lg font-medium">{transaction.type}</h4>
                  <p className="text-sm text-gray-500">
                    {transaction.date}
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <StatusChip status={transaction.status} />
                  <span className="font-semibold text-gray-700 text-lg">
                    {transaction.amount}
                  </span>
                </div>
              </div>
            ))}
          </div>
          {transactions.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>No recent transactions found</p>
            </div>
          )}
        </div>

        {/* Recent Bookings */}
        <div className="bg-white p-5 md:p-6 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-lg relative">
          {isFetching && (
            <div className="absolute top-4 right-4">
              <FaSpinner className="text-blue-500 animate-spin text-sm" />
            </div>
          )}
          <RealtimeIndicator isActive={realTime.indicators.bookings} />
          <div className="flex justify-between items-center mb-5">
            <div className="flex items-center gap-3">
              <h3 className="text-lg font-bold text-gray-800">Recent Bookings</h3>
              {realTime.indicators.bookings && (
                <Chip
                  color="success"
                  variant="dot"
                  size="sm"
                  className="animate-pulse"
                >
                  Updated
                </Chip>
              )}
            </div>
            <CustomButton
              label="View All"
              color="primary"
              variant="light"
              size="sm"
              endContent={<FaEye />}
              onPress={() => console.log('View all bookings')}
            />
          </div>
          <div className="space-y-3">
            {bookings.map((booking, index) => (
              <div
                key={index}
                className="flex flex-col sm:flex-row items-start sm:items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors duration-200 p-4 rounded-lg border border-gray-100"
              >
                <div className="flex items-center space-x-3 md:space-x-4 flex-1">
                  <div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                    <FaUser className="text-blue-600 text-xl" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-base md:text-lg font-medium">{booking.service}</h4>
                    <p className="text-sm text-gray-500">
                      {booking.date}
                    </p>
                    <p className="text-sm text-gray-600 font-medium">{booking.name}</p>
                    <p className="text-xs text-gray-500">{booking.email}</p>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 w-full sm:w-auto mt-3 sm:mt-0">
                  <StatusChip status={booking.status} />
                  {booking.status !== 'Completed' && booking.status !== 'Cancelled' && (
                    <CustomButton
                      color="danger"
                      label="Cancel"
                      size="sm"
                      variant="light"
                      onPress={() => console.log(`Cancel booking for ${booking.name}`)}
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
          {bookings.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>No recent bookings found</p>
            </div>
          )}
        </div>
      </div>

      {/* Real-Time Test Panel (Development Only) */}
      {import.meta.env.DEV && (
        <div className="mt-8">
          <RealTimeTestPanel />
        </div>
      )}
    </div>
  );
}
