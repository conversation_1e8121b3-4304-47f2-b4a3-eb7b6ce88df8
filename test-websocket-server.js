// Simple WebSocket test server for development
// Run with: node test-websocket-server.js

const { Server } = require('socket.io');
const http = require('http');

const server = http.createServer();
const io = new Server(server, {
  cors: {
    origin: "http://localhost:5173", // Vite dev server
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Store connected users
const connectedUsers = new Map();

io.on('connection', (socket) => {
  console.log(`🟢 Client connected: ${socket.id}`);

  // Handle authentication
  socket.on('authenticate', (data) => {
    const { token, userId } = data;
    console.log(`🔐 Authentication attempt for user: ${userId}`);
    
    // Simple token validation (in real app, validate JWT)
    if (token) {
      socket.userId = userId;
      connectedUsers.set(socket.id, { userId, socket });
      socket.emit('authenticated', { success: true, userId });
      console.log(`✅ User authenticated: ${userId}`);
    } else {
      socket.emit('authenticated', { success: false, error: 'Invalid token' });
      console.log(`❌ Authentication failed for user: ${userId}`);
    }
  });

  // Handle dashboard subscription
  socket.on('dashboard:subscribe', (data) => {
    const { userId } = data;
    console.log(`📊 Dashboard subscription for user: ${userId}`);
    
    socket.join(`dashboard:${userId}`);
    socket.emit('dashboard:subscribed', { userId });
    
    // Send initial data
    socket.emit('dashboard:stats:updated', {
      totalOrders: { value: 150, percentage: '+12%', trend: 'up' },
      totalSpend: { value: 2500, percentage: '+8%', trend: 'up' },
      wallet: { value: 500, percentage: '-2%', trend: 'down' },
      totalSavings: { value: 1200, percentage: '+15%', trend: 'up' }
    });
  });

  // Handle dashboard unsubscription
  socket.on('dashboard:unsubscribe', (data) => {
    const { userId } = data;
    console.log(`📊 Dashboard unsubscription for user: ${userId}`);
    socket.leave(`dashboard:${userId}`);
  });

  // Handle disconnection
  socket.on('disconnect', (reason) => {
    console.log(`🔴 Client disconnected: ${socket.id}, reason: ${reason}`);
    connectedUsers.delete(socket.id);
  });

  // Handle errors
  socket.on('error', (error) => {
    console.error(`❌ Socket error for ${socket.id}:`, error);
  });
});

// Simulate real-time updates
const simulateUpdates = () => {
  setInterval(() => {
    // Simulate stats update
    const statsUpdate = {
      totalOrders: { 
        value: Math.floor(Math.random() * 200) + 100, 
        percentage: `${Math.random() > 0.5 ? '+' : '-'}${Math.floor(Math.random() * 20)}%`,
        trend: Math.random() > 0.5 ? 'up' : 'down'
      },
      totalSpend: { 
        value: Math.floor(Math.random() * 5000) + 1000, 
        percentage: `${Math.random() > 0.5 ? '+' : '-'}${Math.floor(Math.random() * 15)}%`,
        trend: Math.random() > 0.5 ? 'up' : 'down'
      },
      wallet: { 
        value: Math.floor(Math.random() * 1000) + 200, 
        percentage: `${Math.random() > 0.5 ? '+' : '-'}${Math.floor(Math.random() * 10)}%`,
        trend: Math.random() > 0.5 ? 'up' : 'down'
      },
      totalSavings: { 
        value: Math.floor(Math.random() * 2000) + 500, 
        percentage: `${Math.random() > 0.5 ? '+' : '-'}${Math.floor(Math.random() * 25)}%`,
        trend: Math.random() > 0.5 ? 'up' : 'down'
      }
    };

    console.log('📊 Broadcasting stats update to all dashboard subscribers');
    io.emit('dashboard:stats:updated', statsUpdate);
  }, 10000); // Every 10 seconds

  setInterval(() => {
    // Simulate transactions update
    const transactionsUpdate = {
      transactions: [
        {
          type: 'Service Payment',
          date: new Date().toLocaleDateString(),
          amount: Math.floor(Math.random() * 500) + 50,
          status: ['Completed', 'Pending', 'In Process'][Math.floor(Math.random() * 3)]
        },
        {
          type: 'Wallet Top-up',
          date: new Date().toLocaleDateString(),
          amount: Math.floor(Math.random() * 200) + 25,
          status: ['Completed', 'Pending'][Math.floor(Math.random() * 2)]
        }
      ]
    };

    console.log('💰 Broadcasting transactions update to all dashboard subscribers');
    io.emit('dashboard:transactions:updated', transactionsUpdate);
  }, 15000); // Every 15 seconds

  setInterval(() => {
    // Simulate bookings update
    const bookingsUpdate = {
      bookings: [
        {
          service: 'House Cleaning',
          date: new Date().toLocaleDateString(),
          customerName: 'John Doe',
          customerEmail: '<EMAIL>',
          status: ['Completed', 'Pending', 'In Process', 'Cancelled'][Math.floor(Math.random() * 4)]
        },
        {
          service: 'Plumbing Service',
          date: new Date().toLocaleDateString(),
          customerName: 'Jane Smith',
          customerEmail: '<EMAIL>',
          status: ['Completed', 'Pending', 'In Process'][Math.floor(Math.random() * 3)]
        }
      ]
    };

    console.log('📅 Broadcasting bookings update to all dashboard subscribers');
    io.emit('dashboard:bookings:updated', bookingsUpdate);
  }, 20000); // Every 20 seconds
};

// Start the server
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`🚀 WebSocket test server running on port ${PORT}`);
  console.log(`🌐 CORS enabled for: http://localhost:5173`);
  console.log(`📊 Dashboard updates will be broadcast every 10-20 seconds`);
  
  // Start simulating updates
  simulateUpdates();
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down WebSocket test server...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});
